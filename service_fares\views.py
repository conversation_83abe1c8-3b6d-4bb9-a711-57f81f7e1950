from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, J<PERSON><PERSON>ars<PERSON>
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from drf_spectacular.utils import extend_schema
from utils.pagination import CustomPagination
from .models import Servicefares, Route
from .serializers import ServicefaresSerializer, RouteSerializer


@extend_schema(
    tags=["Service Fares"]
)
class ServicefaresViewSet(viewsets.ModelViewSet):
    """
    ViewSet for Servicefares model providing CRUD operations.
    """
    queryset = Servicefares.objects.all()
    serializer_class = ServicefaresSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    parser_classes = (JSONParser)
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['route__masTrainType__id','route__masRailwayLine__id','route__name','route__origin','route__destination']
    filterset_fields = ['route__masTrainType__id','route__masRailwayLine__id','route__name','route__origin','route__destination']


@extend_schema(
    tags=["Route"]
)
class RouteViewSet(viewsets.ModelViewSet):
    """
    ViewSet for Route model providing CRUD operations.
    """
    queryset = Route.objects.all()
    serializer_class = RouteSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    parser_classes = (JSONParser)

