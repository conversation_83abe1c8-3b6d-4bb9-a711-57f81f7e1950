from rest_framework import serializers
from RTRDA.serializers import BaseModelSerializer
from .models import Servicefares, Route
from mas.serializers import MasTrainTypeSerializer, MasRailwayLineSerializer


class RouteSerializer(BaseModelSerializer):
    """
    Serializer for Route model
    """
    masTrainType = MasTrainTypeSerializer(read_only=True)
    masRailwayLine = MasRailwayLineSerializer(read_only=True)
    masTrainTypeId = serializers.PrimaryKeyRelatedField(
        source='masTrainType',
        queryset=MasTrainTypeSerializer.Meta.model.objects.all()
    )
    masRailwayLineId = serializers.PrimaryKeyRelatedField(
        source='masRailwayLine',
        queryset=MasRailwayLineSerializer.Meta.model.objects.all()
    )
    class Meta:
        model = Route
        fields = '__all__'  

class ServicefaresSerializer(BaseModelSerializer):
    """
    Serializer for Servicefares model
    """
    route = RouteSerializer(read_only=True)
    class Meta:
        model = Servicefares
        fields = '__all__'
