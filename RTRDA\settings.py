"""
Django settings for RTRDA project.

Generated by 'django-admin startproject' using Django 5.1.6.

For more information on this file, see
https://docs.djangoproject.com/en/5.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.1/ref/settings/
"""

from pathlib import Path
import os
from datetime import timedelta

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "django-insecure-%9z1rh$+@1ftw08m5*y@7ox2h5_n+!-y%a-c5id22%y$+x!vt4"
DRT_API_KEY = "7u8qdRVAAR.Hd1s4pAg7m.qmUmDiv6oH=="
API_VERSION = "v.1.2.0"

# PowerBI API Key Configuration
# Use environment variable for production, fallback to DRT_API_KEY for development
API_KEY = os.environ.get('API_KEY', DRT_API_KEY)

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = os.environ.get('DEBUG', False)

ALLOWED_HOSTS = ['*']


# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "rest_framework",
    'rest_framework_simplejwt',
    'django_filters',
    'drf_spectacular',
    'corsheaders',
    "RTRDA",
    "users",
    "mas",
    "news",
    "trainings",
    "departments",
    "website_statistics",
    "manufacturer",
    "rails",
    "testings",
    "download",
    "course",
    "research",
    "survey",
    "api",
    "components",
    "datacatalog",
    "owner_project",
    "power_bi",
    "mongodb_app",
    "service_fares",
]

# TODO: Setting up Cache configuration
MIDDLEWARE = [
    # 'django.middleware.cache.UpdateCacheMiddleware',
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    'corsheaders.middleware.CorsMiddleware',
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    'RTRDA.middleware.CurrentUserMiddleware',
    'RTRDA.middleware.PowerBIAPIKeyMiddleware',  # PowerBI API key authentication
    'RTRDA.middleware.APIVersionMiddleware',
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    # 'django.middleware.cache.FetchFromCacheMiddleware',
]

ROOT_URLCONF = "RTRDA.urls"

APPEND_SLASH = True

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "RTRDA.wsgi.application"


# Database
# https://docs.djangoproject.com/en/5.1/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'mssql',
        'NAME': os.environ.get('DB_SERVICE', ''),
        'USER': os.environ.get('DB_USER', ''),
        'PASSWORD': os.environ.get('DB_PASSWORD', ''),
        'HOST': os.environ.get('DB_HOST', ''),
        'PORT': os.environ.get('DB_PORT', ''),
        'OPTIONS': {
            'driver': 'ODBC Driver 17 for SQL Server',
            'TrustServerCertificate': True,
        },
    }
}

# MongoDB Configuration
MONGODB_SETTINGS = {
    'host': os.environ.get('MONGODB_HOST', 'localhost'),
    'port': int(os.environ.get('MONGODB_PORT', 27017)),
    'db': os.environ.get('MONGODB_DB', 'rtrda'),
    'username': os.environ.get('MONGODB_USERNAME', ''),
    'password': os.environ.get('MONGODB_PASSWORD', ''),
    'authentication_source': os.environ.get('MONGODB_AUTH_SOURCE', 'admin'),
    'connect': True,
    'maxPoolSize': int(os.environ.get('MONGODB_MAX_POOL_SIZE', 50)),
    'minPoolSize': int(os.environ.get('MONGODB_MIN_POOL_SIZE', 5)),
    'maxIdleTimeMS': int(os.environ.get('MONGODB_MAX_IDLE_TIME_MS', 30000)),
    'serverSelectionTimeoutMS': int(os.environ.get('MONGODB_SERVER_SELECTION_TIMEOUT_MS', 5000)),
    'socketTimeoutMS': int(os.environ.get('MONGODB_SOCKET_TIMEOUT_MS', 20000)),
    'connectTimeoutMS': int(os.environ.get('MONGODB_CONNECT_TIMEOUT_MS', 20000)),
}


# Password validation
# https://docs.djangoproject.com/en/5.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.1/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "Asia/Bangkok"

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.1/howto/static-files/

STATIC_URL = "static/"
STATIC_ROOT = os.path.join(BASE_DIR, 'static')

# Media files
MEDIA_PREFIX = os.environ.get('MEDIA_PREFIX')
MEDIA_URL = os.environ.get('BASE_URL') + MEDIA_PREFIX
MEDIA_ROOT = os.path.join(BASE_DIR, os.environ.get('UPLOAD_DIR'))


# Default primary key field type
# https://docs.djangoproject.com/en/5.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# REST Framework settings
REST_FRAMEWORK = {
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'RTRDA.authentication.CustomJWTAuthentication',
    ],
    'DEFAULT_FILTER_BACKENDS': [
        'django_filters.rest_framework.DjangoFilterBackend',
        'rest_framework.filters.OrderingFilter',
        'rest_framework.filters.SearchFilter',
    ],
    'DEFAULT_SCHEMA_CLASS': 'drf_spectacular.openapi.AutoSchema',
    'DEFAULT_RENDERER_CLASSES': [
        'RTRDA.renderers.VersionedJSONRenderer',
        'rest_framework.renderers.BrowsableAPIRenderer',
    ],
}

AUTHENTICATION_BACKENDS = [
    'django.contrib.auth.backends.ModelBackend',
]

# JWT Settings
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=60),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=1),
    "ROTATE_REFRESH_TOKENS": True,
    "BLACKLIST_AFTER_ROTATION": False,
    "UPDATE_LAST_LOGIN": False,
    "ALGORITHM": "HS256",
    # "SIGNING_KEY": SECRET_KEY,
    "AUTH_HEADER_TYPES": ("Bearer",),
    "TOKEN_TYPE_CLAIM": "token_type",
    "JTI_CLAIM": "jti",
}

# Cache configuration for token tracking
# CACHES = {
#     'default': {
#         'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
#         'LOCATION': 'unique-snowflake',
#     }
# }

# # Cache time to live is 24 hours (in seconds) to match refresh token lifetime
# CACHE_TTL = 60 * 60 * 24

# Add Spectacular settings at the end of the file
SPECTACULAR_SETTINGS = {
    'TITLE': 'RTRDA API',
    'DESCRIPTION': 'API for RTRDA',
    'VERSION': API_VERSION,
    'SERVE_INCLUDE_SCHEMA': False,
}

AUTH_USER_MODEL = 'users.User'

# CORS settings
CORS_ALLOW_ALL_ORIGINS = True  # For development only, set to False in production
# For production, specify allowed origins:
# CORS_ALLOWED_ORIGINS = [
#     "https://example.com",
#     "https://sub.example.com",
#     "http://localhost:8080",
#     "http://127.0.0.1:9000",
# ]

# Additional CORS settings
CORS_ALLOW_METHODS = [
    "DELETE",
    "GET",
    "OPTIONS",
    "PATCH",
    "POST",
    "PUT",
]

CORS_ALLOW_HEADERS = [
    "accept",
    "accept-encoding",
    "authorization",
    "content-type",
    "dnt",
    "origin",
    "user-agent",
    "x-csrftoken",
    "x-requested-with",
]

# Logging configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'INFO',
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
        'mongodb_app': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
        'utils': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}
